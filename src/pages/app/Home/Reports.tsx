import Breadcrumb from '@/components/breadcrumb/Breadcrumb';
import { useFlagcard } from '@/contexts/flagcard-context';
import { ViewerProvider } from '@/contexts/viewer-context/ViewerContext';
import Main from '@/layouts/main/Main';
import { eventTemplate } from '@/services/dojo-api-gateway/story-service/event-template';
import SchReportIcon from '@mui/icons-material/AcUnitSharp';
import DownloadIcon from '@mui/icons-material/Download';
import EventIcon from '@mui/icons-material/Event';

import FilterListIcon from '@mui/icons-material/FilterList';
import ViewColumnIcon from '@mui/icons-material/ViewColumn';

import {
  Badge,
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  IconButton,
  InputLabel,
  Menu,
  MenuItem,
  Radio,
  Select,
  Stack,
  TextField,
  Tooltip,
} from '@mui/material';
import { createTheme, ThemeProvider } from '@mui/material/styles';

import { DateTimePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';

import Cron from 'react-js-cron';
import 'react-js-cron/dist/styles.css';

import {
  DataGridPro,
  getGridBooleanOperators,
  getGridDateOperators,
  getGridNumericOperators,
  getGridStringOperators,
  GridColDef,
  GridColumnOrderChangeParams,
  GridColumnVisibilityModel,
  GridCsvExportOptions,
  GridFilterModel,
  gridFilterModelSelector,
  GridLogicOperator,
  GridPreferencePanelsValue,
  GridSortModel,
  Toolbar,
  useGridApiContext,
  useGridApiRef,
  useGridSelector,
} from '@mui/x-data-grid-pro';

import { storyService } from '@/services/dojo-api-gateway/story-service';
import { story } from '@/services/dojo-api-gateway/story-service/story';
import { Logger } from '@/utils/Logger';
import { useMutation, useQuery } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { useLoader } from '../../../components/loaderIndicator/LoaderContext';
import { SchReportingMockData } from '../../../components/reporting/SchReportingMockData';
import { SchReportCreateEdit } from '@/components/reporting/SchReportCreateEdit';
import { SchReportToolbar } from '@/components/reporting/SchReportToolbar';

function getCustomFilterOperators(type: string) {
  switch (type) {
    case 'boolean':
      return getGridBooleanOperators().filter((op) => op.value !== 'isAnyOf');
    case 'string':
      return getGridStringOperators().filter(
        (op) => op.value !== 'doesNotContain' && op.value !== 'isAnyOf'
      );
    case 'number':
      return getGridNumericOperators().filter((op) => op.value !== 'isAnyOf');
    case 'date':
      //return getGridDateOperators();
      return getGridDateOperators().filter(
        (op) => op.value !== 'isEmpty' && op.value !== 'isNotEmpty'
      );
    case 'dateTime':
      //return getGridDateOperators(true);
      return getGridDateOperators(true).filter(
        (op) => op.value !== 'isEmpty' && op.value !== 'isNotEmpty'
      );
    default:
      return undefined;
  }
}

type FilterMapFormat = {
  [field: string]: {
    [operator: string]: any;
  };
};

const filterOperatorMap: Record<string, string> = {
  contains: 'like',
  equals: 'exact',
  doesNotEqual: '!=',
  startsWith: 'startsWith',
  endsWith: 'endsWith',
  isEmpty: 'isNull',
  isNotEmpty: 'isNotNull',
  '=': '==',
  '!=': '!=',
  '>': '>',
  '>=': '>=',
  '<': '<',
  '<=': '<=',
  is: '==',
  isNot: '!=',
  after: '>',
  onOrAfter: '>=',
  before: '<',
  onOrBefore: '<=',
};

function convertGridFilterModelToMap(
  filterModel: GridFilterModel
): FilterMapFormat {
  const result: FilterMapFormat = {};

  for (const item of filterModel.items) {
    const { field, operator, value } = item;
    if (operator === 'isEmpty' || operator === 'isNotEmpty') {
      item.value = true;
    }
    if (!result[field]) {
      result[field] = {};
    }
    result[field][filterOperatorMap[operator]] = value;
  }
  return result;
}

function Reports() {
  const flagcard = useFlagcard();
  const { showLoader, hideLoader } = useLoader();

  const defaultValue = '30 5 * * 1,6';
  const [value, setValue] = useState('0 0 * * *');

  const [eventSelectPopupOpen, setEventSelectPopupOpen] = useState(true);
  const [schReportPopupOpen, setSchReportPopupOpen] = useState(false);
  const onSchReportPopupOpen = () => setSchReportPopupOpen(true);
  const onSchReportPopupClose = () => setSchReportPopupOpen(false);
  const onSchReportPopupApply = () => {
    // if (selectedEvent && selectedEvent.eventName) {
    //   setSelectedEventName(selectedEvent?.eventName);
    // }
    onSchReportPopupClose();
  };
  const [selectedEvent, setSelectedEvent] = useState({
    eventId: '',
    eventName: '',
  });
  const [selectedEventName, setSelectedEventName] = useState('');
  const [selectedReport, setSelectedReport] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    interval: '',
    enabled: true,
  });

  const [date, setDate] = React.useState<Date | null>(new Date());
  const [time, setTime] = React.useState<Date | null>(new Date());
  const [dateTime, setDateTime] = React.useState<Date | null>(new Date());

  const handleOpen = () => {
    setEventSelectPopupOpen(true);
    refetchEvents().then(({ data }) => {
      setEventList(data ?? []);
    });
  };
  const handleClose = () => {
    setEventList([]);
    setEventSelectPopupOpen(false);
  };

  const handleApply = () => {
    if (selectedEvent && selectedEvent.eventName) {
      setSelectedEventName(selectedEvent?.eventName);
    }
    handleClose();
  };

  const apiRef = useGridApiRef();
  const [rowCount, setRowCount] = useState(0);
  const [data, setData] = useState({ rows: [] as any[], columns: [] as any[] });

  const [columnOrder, setColumnOrder] = useState<string[]>([]);

  const [columnVisibilityModel, setColumnVisibilityModel] =
    useState<GridColumnVisibilityModel>({});

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 25,
    page: 1,
  });
  const [sortModel, setSortModel] = useState<GridSortModel>([]);
  const [filterModel, setFilterModel] = useState<GridFilterModel>({
    items: [],
    logicOperator: GridLogicOperator.And,
  });

  const [progress, setProgress] = useState(0);

  const [eventList, setEventList] = useState<any[]>([]);
  const { refetch: refetchEvents, isLoading: eventsLoading } = useQuery(
    ['ReportsFetchEvents'],
    async () => {
      showLoader();
      const response = await eventTemplate.list();
      hideLoader();
      return response.data?.Data.content;
    },
    {
      onSuccess: (data) => {
        setEventList(data ?? []);
      },
      onError: (error: any) => {
        console.error('Error fetching events:', error);
      },
      refetchOnMount: true,
      refetchOnWindowFocus: false,
      enabled: true,
      staleTime: 30000,
      cacheTime: 3600000,
    }
  );

  // const {
  //   data: events,
  //   refetch: refetchEvents,
  //   isLoading: eventsLoading,
  // } = useQuery(
  //   ['ReportsFetchEvents'],
  //   async () => {
  //     showLoader();
  //     const response = await eventTemplate.list();
  //     hideLoader();
  //     return response.data?.Data.content;
  //   },
  //   {
  //     onError: (error: any) => {
  //       console.error('Error fetching events:', error);
  //     },
  //     refetchOnMount: false,
  //     refetchOnWindowFocus: false,
  //     enabled: true,
  //     staleTime: 30000, // Consider data fresh for 30 seconds
  //     cacheTime: 3600000, // Cache for 1 hour
  //   }
  // );

  const { data: logFields, refetch: refetchLogFields } = useQuery(
    ['logFetchFields'],
    async () => {
      showLoader();
      const response = await storyService.story.logFields(selectedEventName);
      hideLoader();
      return response.data?.Data?.content;
    },
    {
      onError: (error: any) => {
        flagcard.appear({
          type: 'error',
          message: error.message,
        });
        hideLoader();
      },
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      enabled: false,
    }
  );

  // optional manual refetch
  const fetchData = async () => {
    Logger.debug('fetchData');

    if (selectedEventName === '') return;

    showLoader();
    refetchLogFields().then((result) => {
      if (result.data) {
        const fields = result.data;
        Logger.debug('fields loaded:', fields);
        const filters = result.data.reduce((acc: any, field: any) => {
          acc[field.name] = {};
          return acc;
        }, {});
        Logger.debug(filters);

        const filterMap = convertGridFilterModelToMap(filterModel);
        Logger.debug(filterMap);

        // Combine filters with filterMap, prioritizing filterMap values
        const combinedFilters = { ...filters };
        Object.entries(filterMap).forEach(([field, operators]) => {
          combinedFilters[field] = { ...combinedFilters[field], ...operators };
        });
        Logger.debug('Combined filters:', combinedFilters);

        const from = paginationModel.page * paginationModel.pageSize + 1;
        const size = paginationModel.pageSize;

        // Determine sort field and direction
        let sortField = '@timestamp';
        let sortAsc = false;

        if (sortModel.length > 0) {
          sortField = sortModel[0].field;
          sortAsc = sortModel[0].sort === 'asc';
        }

        logQuery
          .mutateAsync({
            eventName: selectedEventName,
            filters: combinedFilters,
            operator: filterModel.logicOperator === 'and' ? 'AND' : 'OR',
            sort: {
              field: sortField,
              asc: sortAsc,
            },
            pagination: {
              page: from,
              size: size,
            },
          })
          .then((responseData) => {
            Logger.debug(responseData);

            // const responseData = await response.json();
            setRowCount(responseData.data.Data.content.numResults);
            const results = responseData.data.Data.content.results;
            // If there are no hits but we have existing columns, keep the current columns
            if (results.length === 0 && data.columns.length > 0) {
              setData((prevData) => ({
                ...prevData,
                rows: [],
              }));
            }

            type LogField = {
              name: string;
              type: string;
            };

            type GridColDef1 = {
              field: string;
              type: 'string' | 'number' | 'boolean' | 'date' | 'dateTime';
            };

            function mapToMuiXColumn(field: LogField): string {
              const typeMap: Record<string, GridColDef1['type']> = {
                Text: 'string',
                Float: 'number',
                Long: 'number',
                Boolean: 'boolean',
                Date: 'dateTime',
              };
              return typeMap[field.type] ?? 'string';
            }

            // Only update columns if we have hits
            if (results.length > 0) {
              const sortedFields = [...fields].sort((a, b) => {
                if (a.name === '@timestamp') return -1;
                if (b.name === '@timestamp') return 1;
                return 0;
              });

              let orderedFields = sortedFields;
              if (columnOrder.length > 0) {
                const order = columnOrder;
                const ordered = columnOrder
                  .map((fieldName) =>
                    sortedFields.find((f) => f.name === fieldName)
                  )
                  .filter(Boolean) as typeof sortedFields;
                const remaining = sortedFields.filter(
                  (f) => !order.includes(f.name)
                );
                orderedFields = [...ordered, ...remaining];
              }

              const columnsFromFields = orderedFields.map(
                (field: LogField, index: number) => {
                  const columnType = mapToMuiXColumn(field);
                  const isHidden = index > 5;
                  return {
                    field: field.name,
                    headerName:
                      field.name.charAt(0).toUpperCase() + field.name.slice(1),
                    flex: 1,
                    minWidth: field.name === '@timestamp' ? 160 : 150,
                    filterable: true,
                    sortable: true,
                    index: index,
                    type: columnType,
                    hidden: isHidden,
                    filterOperators: getCustomFilterOperators(columnType),
                  };
                }
              ) as [];

              // Function to add valueGetter dynamically
              function patchDateTimeColumns(
                columns: GridColDef[]
              ): GridColDef[] {
                return columns.map((col) => {
                  if (
                    col.type === 'dateTime' &&
                    typeof col.valueGetter !== 'function'
                  ) {
                    return {
                      ...col,
                      valueGetter: (value) => {
                        return value ? new Date(value) : null;
                      },
                    };
                  }
                  return col;
                });
              }

              // Use this patched version in your DataGridPro
              const columns = patchDateTimeColumns(columnsFromFields);

              interface Field {
                name: string;
                idx: number;
              }

              if (Object.keys(columnVisibilityModel).length == 0) {
                const newVisibilityModel: { [key: string]: boolean } = {};
                sortedFields.forEach((field: Field, idx: number) => {
                  newVisibilityModel[field.name] = idx < 8;
                });
                // Update the visibility model state
                setColumnVisibilityModel(newVisibilityModel);
              }

              const rows = results.map((result: any, index: number) => {
                return {
                  id: index,
                  ...result,
                };
              });

              setData({
                rows: rows as Array<{ id: string } & Record<string, unknown>>,
                columns: columns as Array<{
                  field: string;
                  headerName: string;
                  flex: number;
                  minWidth: number;
                  filterable: boolean;
                  sortable: boolean;
                  index: number;
                  type: string;
                  hidden: boolean;
                  filterOperators: any;
                }>,
              });
              hideLoader();
            }
          });
      }
    });
  };

  const logQuery = useMutation({
    mutationFn: (values: LogQueryPayload) => {
      return story.logQuery(values);
    },
    onSuccess: (data, variables) => {
      Logger.debug(data);
    },
    onError: ({ response }: any) => {
      hideLoader();
      flagcard.appear({
        type: 'error',
        message: response.data.Message,
        list: response.data.Error.content,
      });
    },
  });

  const [downloadMB, setDownloadMB] = useState<{
    loaded: number;
    total: number | null;
  } | null>(null);

  const logDownload = useMutation({
    mutationFn: async (payload: any) => {
      setDownloadMB({ loaded: 0, total: 10000 });

      const response = await story.logDownload(payload, (loaded, total) => {
        setDownloadMB({ loaded, total });
      });

      if (response.status !== 200) {
        throw {
          response: {
            data: {
              Message: `Server responded with status ${response.status}`,
              Error: response.data,
            },
          },
        };
      }

      const blob = response.data;
      const disposition = response.headers['content-disposition'];
      let filename = `${selectedEventName}-${new Date()
        .toLocaleDateString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
        })
        .replace(/[/:, ]/g, '')}.csv`;
      return { blob, filename };
    },

    onSuccess: ({ blob, filename }) => {
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      setDownloadMB(null);
    },

    onError: ({ response }: any) => {
      setDownloadMB(null);
      hideLoader();
      flagcard.appear({
        type: 'error',
        message: response?.data?.Message || 'Download failed',
        list: response?.data?.Error?.content || [],
      });
    },
  });

  function removeMuiXLicenseWarning(): void {
    try {
      // const style = document.createElement('style');
      // style.innerHTML = `
      //   div[style*="position: absolute"][style*="pointer-events: none"][style*="color: rgba(130, 130, 130, 0.62)"] {
      //   display: none !important;
      //   }
      //   `;
      // document.head.appendChild(style);

      const divs = Array.from(
        document.querySelectorAll<HTMLDivElement>('div[style]')
      );
      const targetDiv = divs.find(
        (div) =>
          div.style.position === 'absolute' &&
          div.style.pointerEvents === 'none' &&
          div.style.color === 'rgba(130, 130, 130, 0.62)' &&
          div.textContent?.trim() === 'MUI X Missing license key'
      );
      if (targetDiv) {
        targetDiv.style.display = 'none';
      }
    } catch (e) {
      console.log(e);
    }
  }

  // setTimeout(() => {
  //   removeMuiXLicenseWarning();
  // }, 500);

  useEffect(() => {
    if (!selectedEventName) {
      handleOpen();
    }
    if (eventList && eventList.length > 0) {
      fetchData();
    }
  }, []);

  useEffect(() => {
    removeMuiXLicenseWarning();
    Logger.debug('rendering....', filterModel.items);
    const filterMap = convertGridFilterModelToMap(filterModel);
    Logger.debug(filterMap);

    // Validate each filter item
    // const isValidFilter = filterModel.items.every(
    //   (item) =>
    //     item.field &&
    //     item.operator &&
    //     item.value !== undefined &&
    //     item.value !== null &&
    //     item.value !== ''
    // );
    fetchData();
  }, [selectedEventName, sortModel, filterModel, paginationModel]);

  const theme = createTheme({
    palette: {
      primary: { main: '#1976d2' },
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            '&.MuiButton-root': {
              color: '#5b3085',
            },
          },
        },
      },
      MuiCheckbox: {
        styleOverrides: {
          root: {
            '&.Mui-checked': {
              color: '#5b3085',
              fillColor: '#5b3085',
            },
            '&.MuiCheckbox-indeterminate': {
              color: '#7544a4',
            },
          },
        },
      },
    },
  });

  const EventSelectorButton = () => {
    return (
      <>
        <div className="flex items-center gap-1">
          <Button
            size="small"
            color="primary"
            startIcon={<EventIcon />}
            onClick={handleOpen}
            sx={{
              '&.MuiButton-root': {
                color: '#5b3085',
              },
            }}
          >
            Events
          </Button>
          {selectedEventName && (
            <div className="flex items-center">
              <div className="w-1 h-1 bg-gray-400 rounded-full mx-2" />
              <span className="text-sm text-gray-600 font-medium">
                {selectedEventName}
              </span>
            </div>
          )}
        </div>
      </>
    );
  };

  const SchReportsButton = () => {
    return (
      <>
        <div className="flex items-center gap-1">
          <Button
            size="small"
            color="primary"
            startIcon={<SchReportIcon />}
            onClick={onSchReportPopupOpen}
            sx={{
              '&.MuiButton-root': {
                color: '#5b3085',
              },
            }}
          >
            Scheduled Reporting
          </Button>
          <SchReportToolbar
            createMode={true}
            eventId={selectedEvent.eventId}
            callback={() => {}}
            close={() => {}}
          />
          {/* {selectedEventName && (
            <div className="flex items-center">
              <div className="w-1 h-1 bg-gray-400 rounded-full mx-2" />
              <span className="text-sm text-gray-600 font-medium">
                {selectedEventName}
              </span>
            </div>
          )} */}
        </div>
      </>
    );
  };

  const CustomFilterButton = React.memo(() => {
    const apiRef = useGridApiContext();
    const filterModel = useGridSelector(apiRef, gridFilterModelSelector);
    const filterCount = filterModel.items.length;
    const buttonRef = React.useRef<HTMLButtonElement | null>(null);

    const handleClick = () => {
      if (buttonRef.current) {
        apiRef.current.showFilterPanel();
      }
    };

    return (
      <Tooltip title="Filters">
        <IconButton ref={buttonRef} onClick={handleClick}>
          <Badge
            badgeContent={filterCount}
            color="primary"
            variant={filterCount > 0 ? 'dot' : undefined}
          >
            <FilterListIcon fontSize="small" />
          </Badge>
        </IconButton>
      </Tooltip>
    );
  });

  const CustomFilterToolbar = React.memo(() => (
    <Toolbar style={{ display: 'flex', justifyContent: 'space-between' }}>
      {/* Left aligned stack */}
      <Stack direction="row" sx={{ gap: 0.5 }}>
        <div className="flex items-center gap-2">
          <EventSelectorButton />
        </div>
        <div>
          <SchReportsButton />
        </div>
      </Stack>

      {/* Right aligned stack */}
      <Stack
        style={{
          display: selectedEventName == '' ? 'none' : 'flex',
          justifyContent: 'flex-end',
        }}
        direction="row"
        sx={{ gap: 0.5 }}
      >
        <CustomFilterButton />
        <ColumnsPanelTrigger />
        {/* <DensitySelector /> */}
        <DataExportMenu />
      </Stack>
    </Toolbar>
  ));

  const ColumnsPanelTrigger = () => {
    const apiRef = useGridApiContext();
    const handleClick = () => {
      apiRef.current.showPreferences(GridPreferencePanelsValue.columns);
    };
    return (
      <Tooltip title="Manage columns">
        <IconButton
          onClick={handleClick}
          aria-label="Manage columns"
          sx={{
            color: '#5b3085',
            '&:hover': {
              backgroundColor: 'rgba(91, 48, 133, 0.04)',
            },
          }}
        >
          <ViewColumnIcon fontSize="small" />
        </IconButton>
      </Tooltip>
    );
  };

  const DataExportMenu = () => {
    const apiRef = useGridApiContext();
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    return (
      <>
        <Tooltip title="Export">
          <IconButton
            onClick={handleClick}
            aria-label="Select to export"
            aria-controls={open ? 'export-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            sx={{
              color: '#5b3085',
              '&:hover': {
                backgroundColor: 'rgba(91, 48, 133, 0.04)',
              },
            }}
          >
            <DownloadIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Menu
          id="export-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          slotProps={{
            list: {
              'aria-labelledby': 'export-button',
              role: 'listbox',
            },
          }}
        >
          <MenuItem
            role="option"
            onClick={() => {
              const options: GridCsvExportOptions = {
                fileName: `${selectedEventName}-${new Date()
                  .toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                  })
                  .replace(/[/:, ]/g, '')}`,
                includeHeaders: true,
                allColumns: false,
              };
              apiRef.current.exportDataAsCsv(options);
              handleClose(); // Close the dropdown after clicking
            }}
          >
            Export
          </MenuItem>
          <MenuItem
            role="option"
            onClick={() => {
              refetchLogFields().then((result) => {
                const fields = result.data;
                Logger.debug('fields loaded:', fields);
                const filters = result.data.reduce((acc: any, field: any) => {
                  acc[field.name] = {};
                  return acc;
                }, {});
                Logger.debug(filters);

                const filterMap = convertGridFilterModelToMap(filterModel);
                Logger.debug(filterMap);

                // Get visible columns
                const visibleColumns = apiRef.current
                  .getVisibleColumns()
                  .map((col) => col.field);

                // Combine filters with filterMap, prioritizing filterMap values
                // Only include visible columns in the filters
                const combinedFilters: Record<string, any> = {};

                // Only add filters for visible columns
                visibleColumns.forEach((field) => {
                  if (filters[field]) {
                  }
                });
                Object.entries(filterMap).forEach(([field, operators]) => {
                  combinedFilters[field] = {
                    ...combinedFilters[field],
                    ...operators,
                  };
                });
                Logger.debug('Combined filters:', combinedFilters);

                // Determine sort field and direction
                let sortField = '@timestamp';
                let sortAsc = false;

                if (sortModel.length > 0) {
                  sortField = sortModel[0].field;
                  sortAsc = sortModel[0].sort === 'asc';
                }

                logDownload
                  .mutateAsync({
                    eventName: selectedEventName,
                    filters: combinedFilters,
                    operator:
                      filterModel.logicOperator === 'and' ? 'AND' : 'OR',
                    sort: {
                      field: sortField,
                      asc: sortAsc,
                    },
                    // pagination: {
                    //   page: from,
                    //   size: size,
                    // },
                  })
                  .then((responseData) => {
                    Logger.debug(responseData);
                    hideLoader();
                  });
              });
            }}
            aria-label="Manage columns"
            sx={{
              color: '#5b3085',
              '&:hover': {
                backgroundColor: 'rgba(91, 48, 133, 0.04)',
              },
            }}
          >
            Export as CSV
          </MenuItem>
        </Menu>
      </>
    );
  };

  const handleColumnOrderChange = (params: GridColumnOrderChangeParams) => {
    if (!apiRef.current) return;
    const visibleColumns = apiRef.current.getVisibleColumns();
    const newOrder = visibleColumns.map((col) => col.field);
    setColumnOrder(newOrder);
    Logger.debug('New visible column order:', newOrder);
  };

  return (
    <>
      <ViewerProvider>
        <Main
          breadcrumb={
            <Breadcrumb
              pages={[
                {
                  name: 'Reports',
                  href: `/app/reports`,
                  current: true,
                },
              ]}
            />
          }
        >
          <div style={{ height: 'calc(100vh - 120px)', width: '100%' }}>
            {/* {false && (
              <div style={{ marginTop: 10 }}>
                <div
                  style={{
                    height: '10px',
                    backgroundColor: '#ddd',
                    borderRadius: '5px',
                    overflow: 'hidden',
                  }}
                >
                  <div
                    style={{
                      width: `${progress}%`,
                      height: '100%',
                      backgroundColor: '#4caf50',
                      transition: 'width 0.2s ease',
                    }}
                  />
                </div>
                <p style={{ fontSize: '0.9rem' }}>{progress}%</p>
              </div>
            )} */}

            {eventSelectPopupOpen && (
              <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
                <div className="w-[400px] h-[400px] bg-[#ededed] shadow-lg rounded-xl p-3 flex flex-col max-h-[600px]">
                  {/* Header */}
                  <div className="flex justify-between items-center border-b border-gray-300 p-3">
                    <h3 className="text-lg font-medium">Select an Event</h3>
                    <button
                      className="text-gray-600 hover:text-gray-800 text-2xl font-light"
                      onClick={handleClose}
                      aria-label="Close"
                    >
                      &times;
                    </button>
                  </div>

                  {/* Content */}
                  <div className="flex-1 overflow-y-auto px-3 py-2">
                    <div className="flex flex-col space-y-3">
                      {eventList?.map((event) => (
                        <label
                          key={event.eventId}
                          className="flex items-center space-x-2 cursor-pointer"
                        >
                          <input
                            type="radio"
                            name="event"
                            value={event.eventId}
                            checked={selectedEvent.eventId === event.eventId}
                            onChange={(e) => {
                              setSelectedEvent({
                                eventId: e.target.value,
                                eventName: event.eventName,
                              });
                            }}
                            style={{
                              accentColor: '#5B3085',
                              color: '#5B3085',
                            }}
                            className="accent-[#5B3085] focus:ring-2 focus:ring-[#5B3085] focus:outline-none"
                          />
                          <span className="text-gray-800 text-sm">
                            {event.eventName}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Footer */}
                  <div className="flex justify-end border-t border-gray-300 pt-3">
                    <button
                      onClick={handleClose}
                      className="px-3 h-[24px] w-[96px] text-white text-xs rounded-md bg-[#999] hover:bg-[#777]"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleApply}
                      disabled={!selectedEvent}
                      className={`px-3 h-[24px] w-[96px] text-white text-xs rounded-md ml-2 flex justify-center items-center ${
                        selectedEvent
                          ? 'bg-[#5B3085] hover:bg-[#472566]'
                          : 'bg-[#999] cursor-not-allowed'
                      }`}
                    >
                      Apply
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* {(state.showCreatePopup || state.showEditPopup) && ( */}
            {schReportPopupOpen && (
              <SchReportCreateEdit
                eventId={selectedEvent.eventId}
                createMode={false}
                callback={(schReport) => {
                  Logger.debug(schReport);
                }}
                close={() => {
                  setSchReportPopupOpen(false);
                }}
              />
            )}

            <ThemeProvider theme={theme}>
              <Box sx={{ height: '100%', width: '100%', position: 'relative' }}>
                <DataGridPro
                  apiRef={apiRef}
                  rows={data.rows}
                  columns={data.columns}
                  rowCount={rowCount}
                  // loading={isLoading}
                  showToolbar
                  density="compact"
                  slots={{ toolbar: CustomFilterToolbar }}
                  disableVirtualization={false}
                  disableColumnMenu={false}
                  pagination
                  paginationMode="server"
                  paginationModel={paginationModel}
                  pageSizeOptions={[25, 50, 100, 250, 500]}
                  onPaginationModelChange={setPaginationModel}
                  filterMode="server"
                  filterModel={filterModel}
                  onFilterModelChange={setFilterModel}
                  sortingMode="client"
                  //sortModel={sortModel}
                  //onSortModelChange={setSortModel}
                  disableRowSelectionOnClick
                  // pinnedColumns={pinnedColumns}
                  disableColumnReorder={false}
                  onColumnOrderChange={handleColumnOrderChange}
                  columnVisibilityModel={columnVisibilityModel}
                  onColumnVisibilityModelChange={(newModel) =>
                    setColumnVisibilityModel(newModel)
                  }
                />
              </Box>
            </ThemeProvider>
            {downloadMB && (
              <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
                <div className="w-[400px] bg-[#ededed] shadow-lg rounded-xl flex flex-col">
                  {/* Header */}
                  <div className="flex justify-between items-center border-b border-gray-300 px-4 pt-4 pb-2">
                    <h3 className="text-lg font-medium">Downloading Report</h3>
                  </div>

                  {/* Content */}
                  <div className="flex-1 px-4 py-5">
                    <div className="flex flex-col items-center">
                      {/* MB Text */}
                      <div className="text-sm font-light text-gray-700 mb-2">
                        {`${downloadMB.loaded} MB downloaded`}
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full h-3 rounded-full bg-gray-300 overflow-hidden">
                        <div
                          className="h-3 bg-[#5B3085] transition-all duration-200 ease-out"
                          style={{
                            width: downloadMB.total
                              ? `${
                                  (downloadMB.loaded / downloadMB.total) * 100
                                }%`
                              : '100%',
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Main>
      </ViewerProvider>
    </>
  );
}

export default Reports;
